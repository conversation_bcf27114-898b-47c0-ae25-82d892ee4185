import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["html", "icon"]

  connect() {
    // Set initial theme based on user preference or system preference
    this.updateTheme(this.getThemePreference())

    // Make sure the icon is updated when the controller connects
    // Use setTimeout to ensure the DOM is fully loaded
    setTimeout(() => {
      this.updateIcon()
    }, 0)

    // Also listen for turbo:render events to update the icon after navigation
    this.boundUpdateIcon = this.updateIcon.bind(this)
    document.addEventListener('turbo:render', this.boundUpdateIcon)
  }

  disconnect() {
    // Clean up event listener when controller is disconnected
    document.removeEventListener('turbo:render', this.boundUpdateIcon)
  }

  toggle() {
    // Toggle between light and dark mode
    const currentTheme = this.getThemePreference()
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark'

    this.updateTheme(newTheme)
    this.saveThemePreference(newTheme)
    this.updateIcon()
  }

  updateTheme(theme) {
    if (theme === 'dark') {
      this.htmlTarget.classList.add('dark')
    } else {
      this.htmlTarget.classList.remove('dark')
    }
  }

  iconTargetConnected() {
    // This is called when the icon target is connected to the DOM
    this.updateIcon()
  }

  updateIcon() {
    // Make sure the icon target exists before trying to update it
    if (!this.hasIconTarget) return

    const currentTheme = this.getThemePreference()

    if (currentTheme === 'dark') {
      // Sun icon for dark mode (clicking will switch to light)
      this.iconTarget.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" />
        </svg>
      `
    } else {
      // Moon icon for light mode (clicking will switch to dark)
      this.iconTarget.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />
        </svg>
      `
    }
  }

  getThemePreference() {
    // Check localStorage first
    // Check if the HTML element already has a dark class (from server-side)
    if (this.htmlTarget.classList.contains('dark')) {
      return 'dark'
    }

    // Default to light
    return 'light'
  }

  saveThemePreference(theme) {
    localStorage.setItem('theme', theme)
  }
}
