<!DOCTYPE html>
<html data-controller="theme" data-theme-target="html">
  <head>
    <title><%= content_for(:title) || "Flamint" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
  </head>

  <body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-200">
    <%= render partial: 'shared/navbar' %>

    <div id="flash_messages" data-controller="flash" data-flash-target="container">
      <% if notice %>
        <div id="flash_message" class="bg-green-100 dark:bg-green-900/50 border border-green-400 dark:border-green-700 text-green-700 dark:text-green-200 px-4 py-3 rounded relative mb-4 mx-4 alert-success" role="alert" data-controller="flash">
          <span class="block sm:inline"><%= notice %></span>
          <button class="absolute top-0 bottom-0 right-0 px-4 py-3 text-gray-400 hover:text-gray-900 dark:hover:text-white" data-action="flash#closeImmediately">
            <svg class="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"/>
            </svg>
          </button>
        </div>
      <% end %>
      <% if alert %>
        <div id="flash_message" class="bg-red-100 dark:bg-red-900/50 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded relative mb-4 mx-4 alert-error" role="alert" data-controller="flash">
          <span class="block sm:inline"><%= alert %></span>
          <button class="absolute top-0 bottom-0 right-0 px-4 py-3 text-gray-400 hover:text-gray-900 dark:hover:text-white" data-action="flash#closeImmediately">
            <svg class="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"/>
            </svg>
          </button>
        </div>
      <% end %>
    </div>

    <%= turbo_stream_from "user_#{current_user.id}_flash" if user_signed_in? %>

    <%= yield %>
  </body>
</html>
