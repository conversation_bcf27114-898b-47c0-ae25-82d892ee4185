<section class="bg-white dark:bg-gray-900">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24 pt-20 sm:pt-24 lg:pt-32">
    <div class="grid items-center gap-8 mb-8 lg:mb-16 lg:gap-12 lg:grid-cols-12">
      <div class="col-span-6 text-center sm:mb-6 lg:text-left lg:mb-0">
        <a href="#"
          class="inline-flex items-center justify-between px-1 py-1 pr-4 mb-6 text-sm text-gray-700 bg-gray-100 rounded-full dark:bg-gray-800 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-700"
          role="alert">
          <span class="px-3 py-1 mr-3 text-xs text-white rounded-full bg-primary-700">New</span> <span
            class="text-sm font-medium">Flowbite is out! See what's new</span>
          <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd"></path>
          </svg>
        </a>
        <h1
          class="mb-4 text-3xl font-extrabold leading-none tracking-tight text-gray-900 sm:text-4xl md:text-5xl xl:text-6xl dark:text-white">
          We invest in the world’s potential</h1>
        <p class="max-w-xl mx-auto mb-6 text-gray-500 lg:mx-0 xl:mb-8 md:text-lg xl:text-xl dark:text-gray-400">
          Here at Flowbite we focus on markets where innovation can unlock long-term value and drive economic growth.
        </p>
        <form class="max-w-lg mx-auto lg:ml-0" action="#">
          <label for="email" class="mb-2 sr-only">Email</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400"
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
              </svg>
            </div>

            <input type="email" id="email"
              class="block w-full py-4 pl-10 pr-4 text-base text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              placeholder="Enter your email here" required="">
          </div>
        </form>
      </div>
      <div class="col-span-6">
        <img src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/hero/search-mockup.png" class="dark:hidden"
          alt="mockup">
        <img src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/hero/search-mockup-dark.png"
          class="hidden dark:block" alt="mockup dark">
      </div>
    </div>

    <div class="grid gap-8 sm:gap-12 md:grid-cols-3">
      <div class="flex justify-center">
        <svg class="w-6 h-6 mr-3 text-primary-700 dark:text-primary-500 shrink-0" fill="currentColor"
          viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd"
            d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z"
            clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="mb-1 text-lg font-semibold leading-tight text-gray-900 dark:text-white">Customizable Categories
          </h3>
          <p class="text-gray-500 dark:text-gray-400">Host code that you don't want to share with the world
            in private.</p>
        </div>
      </div>
      <div class="flex justify-center">
        <svg class="w-6 h-6 mr-3 text-primary-700 dark:text-primary-500 shrink-0" fill="currentColor"
          viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd"
            d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
            clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="mb-1 text-lg font-semibold leading-tight text-gray-900 dark:text-white">Private repos</h3>
          <p class="text-gray-500 dark:text-gray-400">Host code that you don't want to share with the world
            in private.</p>
        </div>
      </div>
      <div class="flex justify-center">
        <svg class="w-6 h-6 mr-3 text-primary-700 dark:text-primary-500 shrink-0" fill="currentColor"
          viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd"
            d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11 4a1 1 0 10-2 0v4a1 1 0 102 0V7zm-3 1a1 1 0 10-2 0v3a1 1 0 102 0V8zM8 9a1 1 0 00-2 0v2a1 1 0 102 0V9z"
            clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="mb-1 text-lg font-semibold leading-tight text-gray-900 dark:text-white">Tracking Saving Rate</h3>
          <p class="text-gray-500 dark:text-gray-400">Host code that you don't want to share with the world
            in private.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="border-t border-b border-gray-100 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
    <div class="space-y-12 lg:space-y-20">
      <div class="items-center gap-8 lg:grid lg:grid-cols-2 xl:gap-16">
        <div class="space-y-4">
          <h2 class="mb-4 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
            Move beyond
          </h2>
          <p class="text-base text-gray-500 sm:text-lg dark:text-gray-400">
            We are strategists, designers and developers. Innovators and problem solvers. Small enough to be simple and
            quick, but
            big enough to deliver the scope you want at the pace you need.
          </p>
          <p class="text-base text-gray-500 sm:text-lg dark:text-gray-400">
            We are strategists, designers and developers. Innovators and problem solvers. Small enough to be simple and
            quick.
          </p>
          <a href="#" title=""
            class="text-gray-900 border bg-white border-gray-200 hover:bg-gray-100 focus:ring-gray-100 hover:text-primary-700 dark:text-gray-400 dark:hover:text-white dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800 justify-center inline-flex items-center focus:ring-4 focus:outline-none  font-medium rounded-lg text-sm px-5 py-2.5 text-center" role="button">
            Try our dashboard
            <svg aria-hidden="true" class="w-5 h-5 ml-2 -mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
              fill="currentColor">
              <path fill-rule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clip-rule="evenodd" />
            </svg>
          </a>
        </div>

        <div class="hidden mb-4 lg:flex lg:mb-0">
          <img class="w-full border border-gray-100 rounded-lg shadow-md dark:hidden"
            src="{{< baseurl >}}/images/billing-mockup.png" alt="">
          <img class="hidden w-full border rounded-lg shadow-md dark:border-gray-700 dark:block"
            src="{{< baseurl >}}/images/billing-mockup-dark.png" alt="">
        </div>
      </div>
    </div>
  </div>
</section>

<section class="bg-white dark:bg-gray-900">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
    <div class="max-w-screen-md mb-8 lg:mb-16">
      <h2 class="mb-4 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
        Designed for business teams like yours
      </h2>
      <p class="text-gray-500 dark:text-gray-400 sm:text-xl">Here at Flowbite we focus on markets where
        technology, innovation, and capital can unlock long-term value and drive economic growth.</p>
    </div>

    <div class="space-y-8 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-8 xl:gap-12 md:space-y-0">
      <div>
        <div class="flex items-center justify-center w-16 h-16 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-12 h-12 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path d="M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z" />
            <path d="M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z" />
            <path d="M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z" />
          </svg>
        </div>

        <div class="mt-5">
          <h3 class="text-xl font-bold dark:text-white">
            Project Management
          </h3>

          <ul role="list" class="mt-4 space-y-4">
            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Unified Contribution Graph
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Org activity graph
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Org dependency insights
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Milestones
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Repo insights
              </span>
            </li>
          </ul>
        </div>
      </div>

      <div>
        <div class="flex items-center justify-center w-16 h-16 bg-purple-100 rounded-lg dark:bg-purple-900">
          <svg class="w-12 h-12 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </div>

        <div class="mt-5">
          <h3 class="text-xl font-bold dark:text-white">
            Collaborative Coding
          </h3>

          <ul role="list" class="mt-4 space-y-4">
            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-purple-600 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Dynamic reports and dashboards
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-purple-600 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Code review assignments
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-purple-600 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Team discussions
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-purple-600 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Protected branches
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-purple-600 bg-purple-100 rounded-full dark:bg-purple-900 dark:text-purple-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Draft pull requests
              </span>
            </li>
          </ul>
        </div>
      </div>

      <div>
        <div class="flex items-center justify-center w-16 h-16 mb-4 bg-teal-100 rounded-lg dark:bg-teal-900">
          <svg class="w-12 h-12 text-teal-600 dark:text-teal-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M6.625 2.655A9 9 0 0119 11a1 1 0 11-2 0 7 7 0 00-9.625-6.492 1 1 0 11-.75-1.853zM4.662 4.959A1 1 0 014.75 6.37 6.97 6.97 0 003 11a1 1 0 11-2 0 8.97 8.97 0 012.25-5.953 1 1 0 011.412-.088z"
              clip-rule="evenodd" />
            <path fill-rule="evenodd"
              d="M5 11a5 5 0 1110 0 1 1 0 11-2 0 3 3 0 10-6 0c0 1.677-.345 3.276-.968 4.729a1 1 0 11-1.838-.789A9.964 9.964 0 005 11zm8.921 2.012a1 1 0 01.831 1.145 19.86 19.86 0 01-.545 2.436 1 1 0 11-1.92-.558c.207-.713.371-1.445.49-2.192a1 1 0 011.144-.83z"
              clip-rule="evenodd" />
            <path fill-rule="evenodd"
              d="M10 10a1 1 0 011 1c0 2.236-.46 4.368-1.29 6.304a1 1 0 01-1.838-.789A13.952 13.952 0 009 11a1 1 0 011-1z"
              clip-rule="evenodd" />
          </svg>
        </div>

        <div class="mt-5">
          <h3 class="text-xl font-bold dark:text-white">
            Enterprise Security
          </h3>

          <ul role="list" class="mt-4 space-y-4">
            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-teal-600 bg-teal-100 rounded-full dark:bg-teal-900 dark:text-teal-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Required reviews
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-teal-600 bg-teal-100 rounded-full dark:bg-teal-900 dark:text-teal-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Dependabot security and
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-teal-600 bg-teal-100 rounded-full dark:bg-teal-900 dark:text-teal-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                Dependency graph
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-teal-600 bg-teal-100 rounded-full dark:bg-teal-900 dark:text-teal-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                GitHub Advisory Database
              </span>
            </li>

            <li class="flex space-x-2.5">
              <div
                class="inline-flex items-center justify-center w-5 h-5 text-teal-600 bg-teal-100 rounded-full dark:bg-teal-900 dark:text-teal-400">
                <svg class="w-3.5 h-3.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <span class="leading-tight text-gray-500 dark:text-gray-400">
                GPG commit signing verification
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="border-t border-b border-gray-100 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
    <div class="max-w-3xl mx-auto mb-8 text-center lg:mb-16">
      <h2 class="text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">
        What you can do with Flowbite
      </h2>
      <p class="mt-4 text-base text-gray-500 dark:text-gray-400 sm:text-xl">
        Here at flowbite we focus on markets where technology, innovation, and capital can unlock long-term value and
        drive
        economic growth.
      </p>
    </div>

    <div class="space-y-8 md:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 md:gap-8 xl:gap-8 md:space-y-0">
      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          Platform
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          We keep Flowbite, secure, and free of spam and abuse so that this can be the platform where developers come
          together to
          create.
        </p>
      </div>

      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          Open source
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          Our Flowbite Security Lab is a world-class security R&D team. We inspire and enable the community to secure
          open source.
        </p>
      </div>

      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          Premium products
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          We embody the shift toward investments in safe and secure software design practices with our world-class
          front-end
          products.
        </p>
      </div>

      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          Customization
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          It's easy to customize and style Flowbite. Tweak the look and feel of your UI with CSS/Less, and add major
          features with
          HTML.
        </p>
      </div>

      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          Features
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          Start with thousands of actions and applications from our community to help you build, improve, and accelerate
          your
          automated workflows.
        </p>
      </div>

      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M6.625 2.655A9 9 0 0119 11a1 1 0 11-2 0 7 7 0 00-9.625-6.492 1 1 0 11-.75-1.853zM4.662 4.959A1 1 0 014.75 6.37 6.97 6.97 0 003 11a1 1 0 11-2 0 8.97 8.97 0 012.25-5.953 1 1 0 011.412-.088z"
              clip-rule="evenodd" />
            <path fill-rule="evenodd"
              d="M5 11a5 5 0 1110 0 1 1 0 11-2 0 3 3 0 10-6 0c0 1.677-.345 3.276-.968 4.729a1 1 0 11-1.838-.789A9.964 9.964 0 005 11zm8.921 2.012a1 1 0 01.831 1.145 19.86 19.86 0 01-.545 2.436 1 1 0 11-1.92-.558c.207-.713.371-1.445.49-2.192a1 1 0 011.144-.83z"
              clip-rule="evenodd" />
            <path fill-rule="evenodd"
              d="M10 10a1 1 0 011 1c0 2.236-.46 4.368-1.29 6.304a1 1 0 01-1.838-.789A13.952 13.952 0 009 11a1 1 0 011-1z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          Data privacy
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          Flowbite is committed to developer privacy and provides a high standard of privacy protection to all our
          customers and
          team.
        </p>
      </div>

      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-2 0c0 .993-.241 1.929-.668 2.754l-1.524-1.525a3.997 3.997 0 00.078-2.183l1.562-1.562C15.802 8.249 16 9.1 16 10zm-5.165 3.913l1.58 1.58A5.98 5.98 0 0110 16a5.976 5.976 0 01-2.516-.552l1.562-1.562a4.006 4.006 0 001.789.027zm-4.677-2.796a4.002 4.002 0 01-.041-2.08l-.08.08-1.53-1.533A5.98 5.98 0 004 10c0 .954.223 1.856.619 2.657l1.54-1.54zm1.088-6.45A5.974 5.974 0 0110 4c.954 0 1.856.223 2.657.619l-1.54 1.54a4.002 4.002 0 00-2.346.033L7.246 4.668zM12 10a2 2 0 11-4 0 2 2 0 014 0z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          Support 24/7
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          We provide high-quality services all around the world with personal assistance through our 24/7 premium
          service.
        </p>
      </div>

      <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <div class="flex items-center justify-center w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900">
          <svg class="w-8 h-8 text-primary-700 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <h3 class="mt-5 text-xl font-bold dark:text-white">
          GDPR
        </h3>
        <p class="mt-2 text-base text-gray-500 dark:text-gray-400">
          Our project is full GDPR compliant. GDPR compliance is shown through our strong and transparent actions, not
          through
          certifications.
        </p>
      </div>
    </div>
  </div>
</section>

<section class="bg-white dark:bg-gray-900">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
    <div class="gap-16 lg:grid lg:grid-cols-3">
      <div class="text-gray-500 sm:text-lg dark:text-gray-400">
        <ul class="block mb-3 space-y-4 sm:flex sm:space-y-0 lg:space-y-4 lg:block " id="testimonialTab" role="tablist">
          <li class="md:mr-2 lg:mr-0" role="presentation">
            <button class="w-full p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" id="testimonial-1-tab"
              type="button" role="tab" aria-controls="testimonial-1" aria-selected="true">
              <figcaption class="space-y-2">
                <div class="flex space-x-2.5">
                  <img class="w-6 h-6 rounded-full"
                    src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/michael-gouch.png"
                    alt="profile picture">
                  <div class="text-lg font-semibold text-gray-900 dark:text-white">Michael Gough</div>
                </div>
                <div class="text-sm text-left text-gray-500 dark:text-gray-400">Web developer at Google</div>
              </figcaption>
            </button>
          </li>
          <li class="md:mr-2 lg:mr-0" role="presentation">
            <button class="w-full p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" id="testimonial-2-tab"
              type="button" role="tab" aria-controls="testimonial-2" aria-selected="false">
              <figcaption class="space-y-2">
                <div class="flex space-x-2.5">
                  <img class="w-6 h-6 rounded-full"
                    src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/karen-nelson.png"
                    alt="profile picture">
                  <div class="text-lg font-semibold text-gray-900 dark:text-white">Bonnie Green</div>
                </div>
                <div class="text-sm text-left text-gray-500 dark:text-gray-400">CEO at Facebook</div>
              </figcaption>
            </button>
          </li>
          <li class="md:mr-2 lg:mr-0" role="presentation">
            <button class="w-full p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" id="testimonial-3-tab"
              type="button" role="tab" aria-controls="testimonial-3" aria-selected="false">
              <figcaption class="space-y-2">
                <div class="flex space-x-2.5">
                  <img class="w-6 h-6 rounded-full"
                    src="https://flowbite.s3.amazonaws.com/blocks/marketing-ui/avatars/roberta-casas.png"
                    alt="profile picture">
                  <div class="text-lg font-semibold text-gray-900 dark:text-white">Lana Byrd</div>
                </div>
                <div class="text-sm text-left text-gray-500 dark:text-gray-400">CTO at Microsoft</div>
              </figcaption>
            </button>
          </li>
        </ul>
        <a class="inline-flex items-center pl-4 text-sm font-medium text-primary-700 hover:underline dark:text-primary-500"
          href="#">
          View other 20 testimonials
          <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd"
              d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
              clip-rule="evenodd"></path>
          </svg>
        </a>
      </div>

      <div class="col-span-2 mt-4 divide-y divide-gray-200 dark:divide-gray-700 lg:mt-0">
        <div id="testimonialTabContent">
          <div class="p-4 rounded-lg" id="testimonial-1" role="tabpanel" aria-labelledby="testimonial-1-tab">
            <svg class="h-8 mb-3 text-gray-500 dark:text-gray-600" viewBox="0 0 24 27" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M14.017 18L14.017 10.609C14.017 4.905 17.748 1.039 23 0L23.995 2.151C21.563 3.068 20 5.789 20 8H24V18H14.017ZM0 18V10.609C0 4.905 3.748 1.038 9 0L9.996 2.151C7.563 3.068 6 5.789 6 8H9.983L9.983 18L0 18Z"
                fill="currentColor" />
            </svg>
            <h3 class="mb-3 text-2xl font-bold leading-tight tracking-tight text-gray-900 dark:text-white">It was a
              great
              experience!</h3>
            <p class="mb-3 text-lg leading-relaxed text-gray-500 dark:text-gray-400">Flowbite is just
              awesome.
              It contains tons of predesigned components and pages starting from login screen to complex dashboard.
              Perfect choice for your next SaaS application.</p>
            <p class="text-lg leading-relaxed text-gray-500 dark:text-gray-400">There is absolutely no doubt
              in
              my mind that without Flowbite, I would not have been able to make the jump to Ueno, a digital agency I
              started in 2014. The work I got through Flowbite made it possible for me to have something to build on. We
              now have about 45 people on our team, a lot of whom we found and recruited through Flowbite.</p>
          </div>
          <div class="hidden p-4 rounded-lg" id="testimonial-2" role="tabpanel" aria-labelledby="testimonial-2-tab">
            <svg class="h-8 mb-3 text-gray-500 dark:text-gray-600" viewBox="0 0 24 27" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M14.017 18L14.017 10.609C14.017 4.905 17.748 1.039 23 0L23.995 2.151C21.563 3.068 20 5.789 20 8H24V18H14.017ZM0 18V10.609C0 4.905 3.748 1.038 9 0L9.996 2.151C7.563 3.068 6 5.789 6 8H9.983L9.983 18L0 18Z"
                fill="currentColor" />
            </svg>
            <h3 class="mb-3 text-2xl font-bold leading-tight tracking-tight text-gray-900 dark:text-white">Best product!
            </h3>
            <p class="mb-3 text-lg leading-relaxed text-gray-500 dark:text-gray-400">Flowbite is just
              awesome.
              It contains tons of predesigned components and pages starting from login screen to complex dashboard.
              Perfect choice for your next SaaS application.</p>
            <p class="text-lg leading-relaxed text-gray-500 dark:text-gray-400"> I would not have been able
              to
              make the jump to Ueno, a digital agency I started in 2014. The work I got through Flowbite made it
              possible
              for me to have something to build on. We now have about 45 people on our team, a lot of whom we found and
              recruited through Flowbite.</p>
          </div>
          <div class="hidden p-4 rounded-lg" id="testimonial-3" role="tabpanel" aria-labelledby="testimonial-3-tab">
            <svg class="h-8 mb-3 text-gray-500 dark:text-gray-600" viewBox="0 0 24 27" fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M14.017 18L14.017 10.609C14.017 4.905 17.748 1.039 23 0L23.995 2.151C21.563 3.068 20 5.789 20 8H24V18H14.017ZM0 18V10.609C0 4.905 3.748 1.038 9 0L9.996 2.151C7.563 3.068 6 5.789 6 8H9.983L9.983 18L0 18Z"
                fill="currentColor" />
            </svg>
            <h3 class="mb-3 text-2xl font-bold leading-tight tracking-tight text-gray-900 dark:text-white">Great design!
            </h3>
            <p class="mb-3 text-lg leading-relaxed text-gray-500 dark:text-gray-400">Flowbite is just
              awesome.
              It contains tons of predesigned components and pages starting from login screen to complex dashboard.
              Perfect choice for your next SaaS application.</p>
            <p class="text-lg leading-relaxed text-gray-500 dark:text-gray-400">There is absolutely no doubt
              in
              my mind that without Flowbite, I would not have been able to make the jump to Ueno, a digital agency I
              started in 2014. The work I got through Flowbite made it possible for me to have something to build on. We
              now have about 45 people on our team, a lot of whom we found and recruited through Flowbite.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="border-t border-b border-gray-100 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
    <div class="bg-white rounded-lg shadow-sm lg:grid lg:grid-cols-3 dark:bg-gray-700">
      <div class="col-span-2 p-6 lg:p-8">
        <h2 class="mb-1 text-2xl font-bold text-gray-900 sm:text-3xl dark:text-white">
          Pricing built for all businesses.
        </h2>
        <p class="text-lg text-gray-500 dark:text-gray-400">
          Best for large scale uses and extended
          redistribution rights.
        </p>

        <div class="grid gap-4 mt-4 lg:mt-6 sm:grid-cols-2 md:grid-cols-3">
          <!-- List -->
          <ul role="list" class="space-y-4 dark:text-white">
            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>
              <span class="leading-tight text-gray-500 dark:text-gray-400">A/B Testing</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">24/7 Chat Support</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Custom Branding</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Creative Assistant</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Website Builder</span>
            </li>
          </ul>

          <!-- List -->
          <ul role="list" class="hidden space-y-4 dark:text-white sm:block">
            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Customer Builder</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Marketing CRM</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Custom Templates</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Creative Assistant</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Multivariate Testing</span>
            </li>
          </ul>

          <!-- List -->
          <ul role="list" class="hidden space-y-4 dark:text-white lg:block">
            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Advanced Tools</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Multivariate Testing</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Reporting</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Custom Templates</span>
            </li>

            <li class="flex space-x-2.5">
              <svg class="shrink-0 w-5 h-5 text-green-500 dark:text-green-400" fill="currentColor"
                viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd" />
              </svg>

              <span class="leading-tight text-gray-500 dark:text-gray-400">Dynamic Content</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="flex p-6 text-center bg-gray-50 lg:p-8 dark:bg-gray-700">
        <div class="self-center w-full">
          <div class="text-5xl font-extrabold text-gray-900 dark:text-white">$99</div>
          <div class="mt-1 mb-4 text-gray-500 text-light dark:text-gray-400">per month</div>
          <a href="#"
            class="flex justify-center text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-bue-200 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700">Buy
            now</a>
          <a href="#"
            class="flex items-center justify-center mt-4 hover:underline font-medium text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
            View team pricing
            <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="bg-white dark:bg-gray-900">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
    <h2
      class="mb-6 text-3xl font-extrabold tracking-tight text-center text-gray-900 lg:mb-8 sm:text-4xl dark:text-white">
      Frequently asked questions
    </h2>

    <div class="max-w-screen-md mx-auto">
      <div id="accordion-flush" data-accordion="collapse"
        data-active-classes="bg-white dark:bg-gray-900 text-gray-900 dark:text-white"
        data-inactive-classes="text-gray-500 dark:text-gray-400">
        <h2 id="accordion-flush-heading-1">
          <button type="button"
            class="flex items-center justify-between w-full py-5 font-medium text-left text-gray-900 bg-white border-b border-gray-200 dark:border-gray-700 dark:bg-gray-900 dark:text-white"
            data-accordion-target="#accordion-flush-body-1" aria-expanded="true" aria-controls="accordion-flush-body-1">
            <span>Can I use Flowbite in open-source projects?</span>
            <svg data-accordion-icon="" class="w-6 h-6 rotate-180 shrink-0" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
          </button>
        </h2>
        <div id="accordion-flush-body-1" class="" aria-labelledby="accordion-flush-heading-1">
          <div class="py-5 border-b border-gray-200 dark:border-gray-700">
            <p class="mb-2 text-gray-500 dark:text-gray-400">Flowbite is an open-source library of interactive
              components built on top of Tailwind CSS including buttons, dropdowns, modals, navbars, and more.</p>
            <p class="mt-2 text-base text-gray-500 dark:text-gray-400">Check out this guide to learn how to <a href="#"
                class="text-primary-700 dark:text-primary-500 hover:underline">get started</a> and start developing
              websites even faster with components on top of Tailwind CSS.</p>
          </div>
        </div>
        <h2 id="accordion-flush-heading-2">
          <button type="button"
            class="flex items-center justify-between w-full py-5 font-medium text-left text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400"
            data-accordion-target="#accordion-flush-body-2" aria-expanded="false"
            aria-controls="accordion-flush-body-2">
            <span>Is there a Figma file available?</span>
            <svg data-accordion-icon="" class="w-6 h-6 shrink-0" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
          </button>
        </h2>
        <div id="accordion-flush-body-2" class="hidden" aria-labelledby="accordion-flush-heading-2">
          <div class="py-5 border-b border-gray-200 dark:border-gray-700">
            <p class="mb-2 text-gray-500 dark:text-gray-400">Flowbite is first conceptualized and designed using the
              Figma software so everything you see in the library has a design equivalent in our Figma file.</p>
            <p class="text-gray-500 dark:text-gray-400">Check out the <a href="#"
                class="text-primary-700 dark:text-primary-500 hover:underline">Figma design system</a> based on the
              utility classes from Tailwind CSS and components from Flowbite.</p>
          </div>
        </div>
        <h2 id="accordion-flush-heading-3">
          <button type="button"
            class="flex items-center justify-between w-full py-5 font-medium text-left text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400"
            data-accordion-target="#accordion-flush-body-3" aria-expanded="false"
            aria-controls="accordion-flush-body-3">
            <span>What are the differences between Flowbite and Tailwind UI?</span>
            <svg data-accordion-icon="" class="w-6 h-6 shrink-0" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
          </button>
        </h2>
        <div id="accordion-flush-body-3" class="hidden" aria-labelledby="accordion-flush-heading-3">
          <div class="py-5 border-b border-gray-200 dark:border-gray-700">
            <p class="mb-2 text-gray-500 dark:text-gray-400">The main difference is that the core components from
              Flowbite are open source under the MIT license, whereas Tailwind UI is a paid product. Another difference
              is that Flowbite relies on smaller and standalone components, whereas Tailwind UI offers sections of
              pages.</p>
            <p class="mb-2 text-gray-500 dark:text-gray-400">However, we actually recommend using both Flowbite,
              Flowbite Pro, and even Tailwind UI as there is no technical reason stopping you from using the best of two
              worlds.</p>
            <p class="mb-2 text-gray-500 dark:text-gray-400">Learn more about these technologies:</p>
            <ul class="pl-5 text-gray-500 list-disc dark:text-gray-400">
              <li><a href="#" class="text-primary-700 dark:text-primary-500 hover:underline">Flowbite Pro</a></li>
              <li><a href="#" class="text-primary-700 dark:text-primary-500 hover:underline">Tailwind UI</a></li>
            </ul>
          </div>
        </div>
        <h2 id="accordion-flush-heading-4">
          <button type="button"
            class="flex items-center justify-between w-full py-5 font-medium text-left text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400"
            data-accordion-target="#accordion-flush-body-4" aria-expanded="false"
            aria-controls="accordion-flush-body-4">
            <span>What about browser support?</span>
            <svg data-accordion-icon="" class="w-6 h-6 shrink-0" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
          </button>
        </h2>
        <div id="accordion-flush-body-4" class="hidden" aria-labelledby="accordion-flush-heading-4">
          <div class="py-5 border-b border-gray-200 dark:border-gray-700">
            <p class="mb-2 text-gray-500 dark:text-gray-400">The main difference is that the core components from
              Flowbite are open source under the MIT license, whereas Tailwind UI is a paid product. Another difference
              is that Flowbite relies on smaller and standalone components, whereas Tailwind UI offers sections of
              pages.</p>
            <p class="mb-2 text-gray-500 dark:text-gray-400">However, we actually recommend using both Flowbite,
              Flowbite Pro, and even Tailwind UI as there is no technical reason stopping you from using the best of two
              worlds.</p>
            <p class="mb-2 text-gray-500 dark:text-gray-400">Learn more about these technologies:</p>
            <ul class="pl-5 text-gray-500 list-disc dark:text-gray-400">
              <li><a href="#" class="text-primary-700 dark:text-primary-500 hover:underline">Flowbite Pro</a></li>
              <li><a href="#" class="text-primary-700 dark:text-primary-500 hover:underline">Tailwind UI</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="border-t border-b border-gray-100 bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
    <div class="max-w-2xl mx-auto text-center">
      <h2 class="mb-4 text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl dark:text-white">Get started with
        Flowbite
        today</h2>
      <p class="mb-6 text-gray-500 md:text-lg dark:text-gray-400">Connecting with your audience has never
        been easier with Flowbite straightforward email marketing and automation tools.</p>
      <form action="#" class="max-w-md mx-auto">
        <div class="sm:flex items-center mb-3">
          <div class="relative w-full mr-3 mb-4 sm:mb-0">
            <label for="member_email" class="hidden mb-2 text-sm font-medium text-gray-900 dark:text-gray-300">Email
              address</label>
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
              </svg>
            </div>
            <input
              class="block w-full p-3 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
              placeholder="Enter your email" type="email" name="member[email]" id="member_email" required="">
          </div>
          <div>
            <input type="submit" value="Try for 30 days"
              class="w-full sm:w-auto px-5 py-3 text-sm font-medium text-center text-white rounded-lg cursor-pointer bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
              name="member_submit" id="member_submit">
          </div>
        </div>
        <div class="text-sm text-left text-gray-500 dark:text-gray-300">Instant signup. No credit card
          required.
        </div>
      </form>
    </div>
  </div>
</section>

<footer class="bg-white dark:bg-gray-900">
  <div class="max-w-screen-xl px-4 py-8 mx-auto sm:py-16 lg:py-24">
      <div class="grid grid-cols-2 gap-8 lg:grid-cols-6">
          <div class="col-span-2">
              <a href="#" class="flex items-center mb-2 text-2xl font-semibold text-gray-900 sm:mb-0 dark:text-white">
                  <svg class="mr-2 h-8" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M25.2696 13.126C25.1955 13.6364 24.8589 14.3299 24.4728 14.9328C23.9856 15.6936 23.2125 16.2264 22.3276 16.4114L18.43 17.2265C17.8035 17.3575 17.2355 17.6853 16.8089 18.1621L14.2533 21.0188C13.773 21.5556 13.4373 21.4276 13.4373 20.7075C13.4315 20.7342 12.1689 23.9903 15.5149 25.9202C16.8005 26.6618 18.6511 26.3953 19.9367 25.6538L26.7486 21.7247C29.2961 20.2553 31.0948 17.7695 31.6926 14.892C31.7163 14.7781 31.7345 14.6639 31.7542 14.5498L25.2696 13.126Z" fill="url(#paint0_linear_11430_22515)"/><path d="M23.5028 9.20133C24.7884 9.94288 25.3137 11.0469 25.3137 12.53C25.3137 12.7313 25.2979 12.9302 25.2694 13.1261L28.0141 14.3051L31.754 14.5499C32.2329 11.7784 31.2944 8.92561 29.612 6.65804C28.3459 4.9516 26.7167 3.47073 24.7581 2.34097C23.167 1.42325 21.5136 0.818599 19.8525 0.486816L17.9861 2.90382L17.3965 5.67918L23.5028 9.20133Z" fill="url(#paint1_linear_11430_22515)"/><path d="M1.5336 11.2352C1.5329 11.2373 1.53483 11.238 1.53556 11.2358C1.67958 10.8038 1.86018 10.3219 2.08564 9.80704C3.26334 7.11765 5.53286 5.32397 8.32492 4.40943C11.117 3.49491 14.1655 3.81547 16.7101 5.28323L17.3965 5.67913L19.8525 0.486761C12.041 -1.07341 4.05728 3.51588 1.54353 11.2051C1.54233 11.2087 1.53796 11.2216 1.5336 11.2352Z" fill="url(#paint2_linear_11430_22515)"/><path d="M19.6699 25.6538C18.3843 26.3953 16.8003 26.3953 15.5147 25.6538C15.3402 25.5531 15.1757 25.4399 15.0201 25.3174L12.7591 26.8719L10.8103 30.0209C12.9733 31.821 15.7821 32.3997 18.589 32.0779C20.7013 31.8357 22.7995 31.1665 24.7582 30.0368C26.3492 29.1191 27.7 27.9909 28.8182 26.7195L27.6563 23.8962L25.7762 22.1316L19.6699 25.6538Z" fill="url(#paint3_linear_11430_22515)"/><path d="M15.0201 25.3175C14.0296 24.5373 13.4371 23.3406 13.4371 22.0588V21.931V11.2558C13.4371 10.6521 13.615 10.5494 14.1384 10.8513C13.3323 10.3864 11.4703 8.79036 9.17118 10.1165C7.88557 10.858 6.8269 12.4949 6.8269 13.978V21.8362C6.8269 24.775 8.34906 27.8406 10.5445 29.7966C10.6313 29.874 10.7212 29.9469 10.8103 30.0211L15.0201 25.3175Z" fill="url(#paint4_linear_11430_22515)"/><path d="M28.6604 5.49565C28.6589 5.49395 28.6573 5.49532 28.6589 5.49703C28.9613 5.83763 29.2888 6.23485 29.6223 6.68734C31.3648 9.05099 32.0158 12.0447 31.4126 14.9176C30.8093 17.7906 29.0071 20.2679 26.4625 21.7357L25.7761 22.1316L28.8181 26.7195C34.0764 20.741 34.09 11.5388 28.6815 5.51929C28.6789 5.51641 28.67 5.50622 28.6604 5.49565Z" fill="url(#paint5_linear_11430_22515)"/><path d="M7.09355 13.978C7.09354 12.4949 7.88551 11.1244 9.17113 10.3829C9.34564 10.2822 9.52601 10.1965 9.71002 10.1231L9.49304 7.38962L7.96861 4.26221C5.32671 5.23364 3.1897 7.24125 2.06528 9.83067C1.2191 11.7793 0.75001 13.9294 0.75 16.1888C0.75 18.0243 1.05255 19.7571 1.59553 21.3603L4.62391 21.7666L7.09355 21.0223V13.978Z" fill="url(#paint6_linear_11430_22515)"/><path d="M9.71016 10.1231C10.8817 9.65623 12.2153 9.74199 13.3264 10.3829L13.4372 10.4468L22.3326 15.5777C22.9566 15.9376 22.8999 16.2918 22.1946 16.4392L22.7078 16.332C23.383 16.1908 23.9999 15.8457 24.4717 15.3428C25.2828 14.4782 25.5806 13.4351 25.5806 12.5299C25.5806 11.0468 24.7886 9.67634 23.503 8.93479L16.6911 5.00568C14.1436 3.53627 11.0895 3.22294 8.29622 4.14442C8.18572 4.18087 8.07756 4.2222 7.96875 4.26221L9.71016 10.1231Z" fill="url(#paint7_linear_11430_22515)"/><path d="M20.0721 31.8357C20.0744 31.8352 20.0739 31.8332 20.0717 31.8337C19.6252 31.925 19.1172 32.0097 18.5581 32.0721C15.638 32.3978 12.7174 31.4643 10.5286 29.5059C8.33986 27.5474 7.09347 24.7495 7.09348 21.814L7.09347 21.0222L1.59546 21.3602C4.1488 28.8989 12.1189 33.5118 20.0411 31.8421C20.0449 31.8413 20.0582 31.8387 20.0721 31.8357Z" fill="url(#paint8_linear_11430_22515)"/>
                      <defs>
                      <linearGradient id="paint0_linear_11430_22515" x1="20.8102" y1="23.9532" x2="23.9577" y2="12.9901" gradientUnits="userSpaceOnUse"><stop stop-color="#1724C9"/><stop offset="1" stop-color="#1C64F2"/></linearGradient>
                      <linearGradient id="paint1_linear_11430_22515" x1="28.0593" y1="10.5837" x2="19.7797" y2="2.33321" gradientUnits="userSpaceOnUse"><stop stop-color="#1C64F2"/><stop offset="1" stop-color="#0092FF"/></linearGradient>
                      <linearGradient id="paint2_linear_11430_22515" x1="16.9145" y1="5.2045" x2="4.42432" y2="5.99375" gradientUnits="userSpaceOnUse"><stop stop-color="#0092FF"/><stop offset="1" stop-color="#45B2FF"/></linearGradient>
                      <linearGradient id="paint3_linear_11430_22515" x1="16.0698" y1="28.846" x2="27.2866" y2="25.8192" gradientUnits="userSpaceOnUse"><stop stop-color="#1C64F2"/><stop offset="1" stop-color="#0092FF"/></linearGradient>
                      <linearGradient id="paint4_linear_11430_22515" x1="8.01881" y1="15.8661" x2="15.9825" y2="24.1181" gradientUnits="userSpaceOnUse"><stop stop-color="#1724C9"/><stop offset="1" stop-color="#1C64F2"/></linearGradient>
                      <linearGradient id="paint5_linear_11430_22515" x1="26.2004" y1="21.8189" x2="31.7569" y2="10.6178" gradientUnits="userSpaceOnUse"><stop stop-color="#0092FF"/><stop offset="1" stop-color="#45B2FF"/></linearGradient>
                      <linearGradient id="paint6_linear_11430_22515" x1="6.11387" y1="9.31427" x2="3.14054" y2="20.4898" gradientUnits="userSpaceOnUse"><stop stop-color="#1C64F2"/><stop offset="1" stop-color="#0092FF"/></linearGradient>
                      <linearGradient id="paint7_linear_11430_22515" x1="21.2932" y1="8.78271" x2="10.4278" y2="11.488" gradientUnits="userSpaceOnUse"><stop stop-color="#1724C9"/><stop offset="1" stop-color="#1C64F2"/></linearGradient>
                      <linearGradient id="paint8_linear_11430_22515" x1="7.15667" y1="21.5399" x2="14.0824" y2="31.9579" gradientUnits="userSpaceOnUse"><stop stop-color="#0092FF"/><stop offset="1" stop-color="#45B2FF"/></linearGradient>
                      </defs>
                  </svg>
                  Flowbite
              </a>
              <p class="my-4  text-gray-500 dark:text-gray-400">Flowbite is a open-source library of over 400+ web components and interactive elements built with the utility classes from Tailwind CSS.</p>
              <ul class="flex mt-5 space-x-6">
                  <li>
                      <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white dark:text-gray-400">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" /></svg>
                      </a>
                  </li>
                  <li>
                      <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white dark:text-gray-400">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" /></svg>
                      </a>
                  </li>
                  <li>
                      <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white dark:text-gray-400">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" /></svg>
                      </a>
                  </li>
                  <li>
                      <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white dark:text-gray-400">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd" /></svg>
                      </a>
                  </li>
                  <li>
                      <a href="#" class="text-gray-500 hover:text-gray-900 dark:hover:text-white dark:text-gray-400">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"><path fill-rule="evenodd" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c5.51 0 10-4.48 10-10S17.51 2 12 2zm6.605 4.61a8.502 8.502 0 011.93 5.314c-.281-.054-3.101-.629-5.943-.271-.065-.141-.12-.293-.184-.445a25.416 25.416 0 00-.564-1.236c3.145-1.28 4.577-3.124 4.761-3.362zM12 3.475c2.17 0 4.154.813 5.662 2.148-.152.216-1.443 1.941-4.48 3.08-1.399-2.57-2.95-4.675-3.189-5A8.687 8.687 0 0112 3.475zm-3.633.803a53.896 53.896 0 013.167 4.935c-3.992 1.063-7.517 1.04-7.896 1.04a8.581 8.581 0 014.729-5.975zM3.453 12.01v-.26c.37.01 4.512.065 8.775-1.215.25.477.477.965.694 1.453-.109.033-.228.065-.336.098-4.404 1.42-6.747 5.303-6.942 5.629a8.522 8.522 0 01-2.19-5.705zM12 20.547a8.482 8.482 0 01-5.239-1.8c.152-.315 1.888-3.656 6.703-5.337.022-.01.033-.01.054-.022a35.318 35.318 0 011.823 6.475 8.4 8.4 0 01-3.341.684zm4.761-1.465c-.086-.52-.542-3.015-1.659-6.084 2.679-.423 5.022.271 5.314.369a8.468 8.468 0 01-3.655 5.715z" clip-rule="evenodd" /></svg>
                      </a>
                  </li>
              </ul>
          </div>
          <div class="lg:mx-auto">
              <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Company</h2>
              <ul class="text-gray-500 dark:text-gray-400">
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">About</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Careers</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Brand Center</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Blog</a>
                  </li>
              </ul>
          </div>
          <div class="lg:mx-auto">
              <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Help center</h2>
              <ul class="text-gray-500 dark:text-gray-400">
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Discord Server</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Twitter</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Facebook
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Contact Us</a>
                  </li>
              </ul>
          </div>
          <div class="lg:mx-auto">
              <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Legal</h2>
              <ul class="text-gray-500 dark:text-gray-400">
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Privacy Policy</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Licensing</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Terms</a>
                  </li>
              </ul>
          </div>
          <div class="lg:mx-auto">
              <h2 class="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white">Download</h2>
              <ul class="text-gray-500 dark:text-gray-400">
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">iOS</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Android</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Windows</a>
                  </li>
                  <li class="mb-4">
                      <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">MacOS</a>
                  </li>
              </ul>
          </div>
      </div>
      <hr class="my-6 border-gray-200 sm:mx-auto dark:border-gray-800 lg:my-8">
      <span class="block text-sm text-center text-gray-500 dark:text-gray-400">© 2021-<span id="currentYear">2022</span> <a href="#" class="hover:underline hover:text-gray-900 dark:hover:text-white">Flowbite™</a>. All Rights Reserved.</span>
  </div>
</footer>
