<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-white">Your Trading Bots</h1>
      <div class="flex space-x-2">
        <%= link_to "Create Copier Bot", new_bot_path(type: 'Copier'), class: "bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700" %>
        <%= link_to "Create Trader Bot", new_bot_path(type: 'Trader'), class: "bg-pink-600 text-white px-3 py-2 rounded hover:bg-pink-700 opacity-50 cursor-not-allowed", disabled: true %>
      </div>
    </div>

    <% if @bots.empty? %>
      <div class="bg-gray-700 p-6 rounded-lg text-center">
        <p class="text-gray-300 mb-4">You don't have any trading bots yet.</p>
        <p class="text-gray-400">Create your first bot to start automated trading.</p>
      </div>
    <% else %>
      <div class="overflow-x-auto">
        <table class="min-w-full bg-gray-700 rounded-lg overflow-hidden">
          <thead class="bg-gray-600">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Type</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">From Account</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">To Accounts</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-600">
            <% @bots.each do |bot| %>
              <tr>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  <% if bot.type == 'Copier' %>
                    <span class="px-2 py-1 text-xs rounded-full bg-purple-800 text-purple-200">Copier</span>
                  <% else %>
                    <span class="px-2 py-1 text-xs rounded-full bg-pink-800 text-pink-200">Trader</span>
                  <% end %>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  <% if bot.respond_to?(:from_account) && bot.from_account %>
                    <%= bot.from_account.name || "Account ##{bot.from_account.id}" %>
                    <span class="text-xs text-gray-400">
                      (<%= bot.from_account.accountable_type.sub('Account', '') %>)
                    </span>
                  <% else %>
                    <span class="text-gray-500">None</span>
                  <% end %>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  <% if bot.respond_to?(:to_accounts) && bot.to_accounts.any? %>
                    <div class="flex flex-wrap gap-1">
                      <% bot.to_accounts.each do |account| %>
                        <span class="px-2 py-1 text-xs rounded-full bg-blue-800 text-blue-200">
                          <%= account.name || "Account ##{account.id}" %>
                        </span>
                      <% end %>
                    </div>
                  <% else %>
                    <span class="text-gray-500">None</span>
                  <% end %>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <%= turbo_frame_tag "bot_status_#{bot.id}" do %>
                    <% status_class = case bot.expect_status
                      when 'running'
                        'bg-green-800 text-green-200'
                      when 'stopped'
                        'bg-red-800 text-red-200'
                      else
                        'bg-gray-600 text-gray-200'
                    end %>
                    <%= render 'bot_status_content', bot: bot, status_class: status_class, status_text: bot.expect_status.capitalize %>
                  <% end %>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                  <div class="flex space-x-2">
                    <%= link_to "Edit", edit_bot_path(bot), class: "text-blue-400 hover:text-blue-300" %>
                    <%= button_to( (bot.stopped? ? "Start" : 'Restart'), start_bot_path(bot), method: :post, class: "text-green-400 hover:text-green-300 bg-transparent border-none cursor-pointer", data: { turbo: true } )%>
                    <%= button_to "Stop", stop_bot_path(bot), method: :post, class: "text-yellow-400 hover:text-yellow-300 bg-transparent border-none cursor-pointer", data: { turbo: true } %>
                    <%= link_to "View Logs", logs_bot_path(bot), class: "text-cyan-400 hover:text-cyan-300" %>
                    <%= button_to "Delete", bot_path(bot), method: :delete, class: "text-red-400 hover:text-red-300 bg-transparent border-none cursor-pointer", data: { confirm: "Are you sure you want to delete this bot?" } %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    <% end %>

    <div class="mt-6">
      <%= link_to "Back to Dashboard", user_dashboard_path, class: "text-indigo-400 hover:text-indigo-300" %>
    </div>
  </div>
</div>

<% @bots.each do |bot| %>
  <%= turbo_stream_from "bot_#{bot.id}_status" %>
<% end %>

