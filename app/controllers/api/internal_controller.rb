class Api::InternalController < ActionController::API
  before_action :authenticate_internal_request

  # POST /api/internal/container/mt5_server_search
  def mt5_server_search
    server_name = params[:server_name]

    if server_name.blank?
      render json: { error: 'Server name is required' }, status: :bad_request
      return
    end

    # Execute the Docker command
    begin
      container_name = params[:container_name] || 'mt5'
      command = "docker exec -u 911:911 #{container_name} wine cmd /c \"python /scripts/mt5serversearch.py #{server_name}\""

      # Log the command being executed
      Rails.logger.info("Executing MT5 server search command: #{command}")

      # Execute the command and capture output
      output = `#{command} 2>&1`
      exit_status = $?.exitstatus

      if exit_status == 0
        render json: {
          success: true,
          container: container_name,
          server_name: server_name,
          output: output.strip
        }
      else
        render json: {
          success: false,
          container: container_name,
          server_name: server_name,
          error: output.strip,
          exit_status: exit_status
        }, status: :internal_server_error
      end
    rescue => e
      Rails.logger.error("Error executing MT5 server search: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  def mt5_escape_window
    # Execute the Docker command
    begin
      container_name = params[:container_name] || 'mt5'
      command = "docker exec -u 911:911 #{container_name} wine cmd /c \"python /scripts/sendescapetomt5.py\""

      # Log the command being executed
      Rails.logger.info("Executing MT5 escape window: #{command}")

      # Execute the command and capture output
      output = `#{command} 2>&1`
      exit_status = $?.exitstatus

      if exit_status == 0
        render json: {
          success: true,
          container: container_name,
          output: output.strip
        }
      else
        render json: {
          success: false,
          container: container_name,
          error: output.strip,
          exit_status: exit_status
        }, status: :internal_server_error
      end
    rescue => e
      Rails.logger.error("Error executing MT5 escape window: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  # POST /api/internal/script_notification
  def script_notification
    script_id = params[:script_id]
    status = params[:status]
    message = params[:message]

    if script_id.blank? || status.blank?
      render json: { error: 'Script ID and status are required' }, status: :bad_request
      return
    end

    # Find the script
    script = Script.find_by(id: script_id)

    unless script
      render json: { error: 'Script not found' }, status: :not_found
      return
    end

    # Log the notification
    Rails.logger.info("Script notification received: Script ID: #{script_id}, Status: #{status}, Message: #{message}")

    # Update script status in database if needed
    # This would depend on your application's requirements

    # You could also notify users via ActionCable or other means

    render json: {
      success: true,
      script_id: script_id,
      status: status,
      message: "Notification received for script #{script_id}"
    }
  end

  # POST /api/internal/bot_restart
  def bot_restart
    script_id = params[:script_id]

    if script_id.blank?
      render json: { error: 'Script ID is required' }, status: :bad_request
      return
    end

    # Find the script
    script = Script.find_by(id: script_id)

    unless script
      render json: { error: 'Script not found' }, status: :not_found
      return
    end

    begin
      # Queue the job to restart the bot
      BotActionJob.perform_later('restart', script.id, script.user_id)

      # Log the restart request
      Rails.logger.info("Bot restart request received: Script ID: #{script_id}")

      render json: {
        success: true,
        script_id: script_id,
        message: "Bot restart request queued successfully"
      }
    rescue => e
      Rails.logger.error("Error queuing bot restart: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  # GET /api/internal/get_expect_running_script_ids
  def get_expect_running_script_ids
    begin
      # Get all scripts that are expected to be running
      script_ids = Script.expect_running.pluck(:id)

      # Log the request
      Rails.logger.info("Expected running script IDs requested: #{script_ids}")

      render json: {
        success: true,
        script_ids: script_ids,
        count: script_ids.length
      }
    rescue => e
      Rails.logger.error("Error retrieving expected running script IDs: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  # POST /api/internal/update_script_expect_status
  def update_script_expect_status
    script_id = params[:script_id]
    expect_status = params[:expect_status]

    if script_id.blank? || expect_status.blank?
      render json: { error: 'Script ID and expect_status are required' }, status: :bad_request
      return
    end

    # Find the script
    script = Script.find_by(id: script_id)

    unless script
      render json: { error: 'Script not found' }, status: :not_found
      return
    end

    begin
      # Update the expect_status
      script.update!(expect_status: expect_status)

      # Log the update
      Rails.logger.info("Updated expect_status for script #{script_id} to: #{expect_status}")

      broadcast_status_update(script)

      render json: {
        success: true,
        script_id: script_id,
        expect_status: expect_status,
        message: "Script expect_status updated successfully"
      }
    rescue => e
      Rails.logger.error("Error updating expect_status for script #{script_id}: #{e.message}")
      render json: {
        success: false,
        error: e.message
      }, status: :internal_server_error
    end
  end

  private

  def authenticate_internal_request
    client_ip = request.remote_ip
    auth_key = params[:auth_key] || request.headers['X-Internal-Auth-Key']

    # Log the authentication attempt
    Rails.logger.info("Internal API authentication attempt from IP: #{client_ip}")

    # Step 1: Validate authentication key (REQUIRED)
    expected_auth_key = ENV['INTERNAL_API_AUTH_KEY']

    if auth_key.blank?
      Rails.logger.warn("Internal API access denied: No authentication key provided from IP: #{client_ip}")
      render json: { error: 'Unauthorized: Authentication key required' }, status: :unauthorized
      return false
    end

    if expected_auth_key.blank?
      Rails.logger.error("Internal API configuration error: INTERNAL_API_AUTH_KEY environment variable not set")
      render json: { error: 'Internal server error' }, status: :internal_server_error
      return false
    end

    unless ActiveSupport::SecurityUtils.secure_compare(auth_key, expected_auth_key)
      Rails.logger.warn("Internal API access denied: Invalid authentication key provided from IP: #{client_ip}")
      render json: { error: 'Unauthorized: Invalid authentication key' }, status: :unauthorized
      return false
    end

    # Step 2: Validate IP address (REQUIRED)
    unless valid_source_ip?(client_ip)
      Rails.logger.warn("Internal API access denied: Invalid source IP #{client_ip} with valid auth key")
      render json: { error: 'Unauthorized: Invalid source IP' }, status: :unauthorized
      return false
    end

    # Both auth key and IP are valid
    Rails.logger.info("Internal API access granted to IP: #{client_ip}")
    true
  end

  def valid_source_ip?(client_ip)
    # Allow localhost for development and local testing
    return true if ['127.0.0.1', '::1', 'localhost'].include?(client_ip)

    # Check if the request is coming from the Kamal Docker network
    kamal_networks = get_kamal_network_cidrs

    return false if kamal_networks.empty?

    # Check against actual Kamal network CIDRs
    kamal_networks.any? { |network| network.include?(client_ip) }
  rescue => e
    Rails.logger.error("Error validating source IP #{client_ip}: #{e.message}")
    false
  end

  def broadcast_status_update(bot)
    # Get current status from script manager
    status_class = 'bg-gray-600 text-gray-200'
    status_text = 'Unknown'

    case bot.expect_status
    when 'running'
      status_class = 'bg-green-800 text-green-200'
      status_text = 'Running'
    when 'stopped'
      status_class = 'bg-red-800 text-red-200'
      status_text = 'Stopped'
    when 'not_found'
      status_class = 'bg-yellow-800 text-yellow-200'
      status_text = 'Not Started'
    when 'error'
      status_class = 'bg-red-800 text-red-200'
      status_text = 'Error'
    end

    # Broadcast the status update
    Turbo::StreamsChannel.broadcast_update_to(
      "bot_#{bot.id}_status",
      target: "bot_status_#{bot.id}",
      partial: 'bots/bot_status_content',
      locals: {
        bot: bot,
        status_class: status_class,
        status_text: status_text
      }
    )
  end

  def get_kamal_network_cidrs
    # Cache the network info for 5 minutes to avoid repeated Docker API calls
    Rails.cache.fetch('kamal_network_cidrs', expires_in: 5.minutes) do
      begin
        # Get the Kamal network information using Docker API
        networks = Docker::Network.all
        kamal_network = networks.find { |network| network.info['Name'] == 'kamal' }

        if kamal_network.nil?
          Rails.logger.warn("Kamal network not found in Docker networks")
          return []
        end

        # Extract IPAM configuration to get the subnet CIDRs
        ipam_config = kamal_network.info.dig('IPAM', 'Config')
        if ipam_config.nil? || ipam_config.empty?
          Rails.logger.warn("No IPAM configuration found for Kamal network")
          return []
        end

        # Convert subnet strings to IPAddr objects
        cidrs = ipam_config.map do |config|
          subnet = config['Subnet']
          next unless subnet

          begin
            IPAddr.new(subnet)
          rescue IPAddr::InvalidAddressError => e
            Rails.logger.warn("Invalid subnet format in Kamal network: #{subnet} - #{e.message}")
            nil
          end
        end.compact

        Rails.logger.info("Detected Kamal network CIDRs: #{cidrs.map(&:to_s).join(', ')}")
        cidrs
      rescue => e
        Rails.logger.error("Error detecting Kamal network CIDRs: #{e.message}")
        []
      end
    end
  end
end
