import os
import json
import subprocess
import psutil
import time
import logging
from typing import Dict, Any, Optional
import signal
from internal_requests import get_expect_running_script_ids, update_script_expect_status
import sys

# Configure more detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ScriptProcessManager:
    def __init__(self):
        self.active_processes: Dict[int, subprocess.Popen] = {}
        self.script_statuses: Dict[int, Dict[str, Any]] = {}
        self.process_logs: Dict[int, Dict[str, Any]] = {}

        # Auto-restart scripts that were expected to be running
        self._auto_restart_expected_scripts()

    def _get_config(self):
        """Get configuration for internal API requests"""
        return {
            'API_BASE_URL': os.environ.get('API_BASE_URL', 'http://web:80'),
            'INTERNAL_API_AUTH_KEY': os.environ.get('INTERNAL_API_AUTH_KEY', '')
        }

    def start_script(self, script_id: int) -> bool:
        """Start a trading script process for a user"""
        # Stop existing process if it exists
        if script_id in self.active_processes:
            logger.info(f"Stopping existing script {script_id} before starting a new one")
            self.stop_script(script_id)

        try:
            # Create logs directory if it doesn't exist
            os.makedirs('./logs', exist_ok=True)

            # Open log files for the script
            stdout_log_path = f'./logs/script_{script_id}_stdout.log'
            stderr_log_path = f'./logs/script_{script_id}_stderr.log'

            # Add timestamp to log files
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            with open(stdout_log_path, 'a') as f:
                f.write(f"\n\n--- Script {script_id} started at {timestamp} ---\n\n")
            with open(stderr_log_path, 'a') as f:
                f.write(f"\n\n--- Script {script_id} started at {timestamp} ---\n\n")

            # Set environment variables for the script
            env = os.environ.copy()
            env['SCRIPT_ID'] = str(script_id)

            # Start the script with the script_id as an argument
            # Use file redirection for logs
            stdout_file = open(stdout_log_path, 'a')
            stderr_file = open(stderr_log_path, 'a')

            process = subprocess.Popen(
                ["python3", "main.py", str(script_id)],
                stdout=stdout_file,
                stderr=stderr_file,
                text=True,
                env=env,
                # Use preexec_fn to ensure the subprocess gets its own process group
                # This allows it to receive signals independently
                preexec_fn=os.setsid if hasattr(os, 'setsid') else None
            )

            self.active_processes[script_id] = process
            self.script_statuses[script_id] = {
                "status": "running",
                "started_at": time.time(),
                "last_heartbeat": time.time(),
                "pid": process.pid,
                "stdout_log": stdout_log_path,
                "stderr_log": stderr_log_path
            }

            # Store log file handles for cleanup
            self.process_logs[script_id] = {
                'stdout': stdout_file,
                'stderr': stderr_file
            }

            logger.info(f"Started script id {script_id} with PID {process.pid}, logs in {stdout_log_path} and {stderr_log_path}")

            # Update expect_status to running in database
            try:
                config = self._get_config()
                update_script_expect_status(config, script_id, 'running')
            except Exception as e:
                logger.warning(f"Failed to update expect_status for script {script_id}: {str(e)}")

            return True

        except Exception as e:
            logger.error(f"Failed to start script id {script_id}: {str(e)}", exc_info=True)
            # Close log files if they were opened
            if 'stdout_file' in locals() and stdout_file:
                stdout_file.close()
            if 'stderr_file' in locals() and stderr_file:
                stderr_file.close()
            return False

    def stop_script(self, script_id: int) -> bool:
        """Stop a running script process for a user"""
        if script_id not in self.active_processes:
            logger.warning(f"Cannot stop script {script_id}: not running")
            # Update expect_status to stopped in database
            try:
                config = self._get_config()
                update_script_expect_status(config, script_id, 'stopped')
            except Exception as e:
                logger.warning(f"Failed to update expect_status for script {script_id}: {str(e)}")
            return False

        process = self.active_processes[script_id]
        try:
            # Log the stop action
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"Attempting to stop script {script_id} at {timestamp}")

            # Try to terminate gracefully first by sending SIGTERM
            try:
                # Try to send SIGTERM to the process group if on Unix
                if hasattr(os, 'killpg') and hasattr(os, 'getpgid'):
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                else:
                    process.terminate()

                logger.debug(f"Sent SIGTERM to script {script_id}")
            except Exception as e:
                logger.warning(f"Error sending SIGTERM to script {script_id}: {str(e)}")
                # Continue with the termination process even if SIGTERM fails

            # Wait a bit for graceful termination
            termination_timeout = 5  # seconds
            termination_start = time.time()

            while time.time() - termination_start < termination_timeout:
                if process.poll() is not None:
                    logger.info(f"Script {script_id} terminated gracefully")
                    break
                time.sleep(0.5)

            # Force kill if still running
            if process.poll() is None:
                logger.warning(f"Script {script_id} did not terminate gracefully, sending SIGKILL")
                try:
                    # Try to send SIGKILL to the process group if on Unix
                    if hasattr(os, 'killpg') and hasattr(os, 'getpgid'):
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                    else:
                        process.kill()
                except Exception as e:
                    logger.error(f"Error sending SIGKILL to script {script_id}: {str(e)}")
                    # If we can't kill it, we'll still try to clean up

            # Close log files if they exist
            if script_id in self.process_logs:
                try:
                    logger.debug(f"Closing log files for script {script_id}")
                    if 'stdout' in self.process_logs[script_id]:
                        self.process_logs[script_id]['stdout'].close()
                    if 'stderr' in self.process_logs[script_id]:
                        self.process_logs[script_id]['stderr'].close()

                    # Add a stop marker to the log files
                    if script_id in self.script_statuses:
                        stdout_log = self.script_statuses[script_id].get("stdout_log")
                        stderr_log = self.script_statuses[script_id].get("stderr_log")

                        if stdout_log:
                            with open(stdout_log, 'a') as f:
                                f.write(f"\n\n--- Script {script_id} stopped at {timestamp} ---\n\n")
                        if stderr_log:
                            with open(stderr_log, 'a') as f:
                                f.write(f"\n\n--- Script {script_id} stopped at {timestamp} ---\n\n")
                except Exception as e:
                    logger.error(f"Error closing log files for script {script_id}: {str(e)}")

                # Remove log file handles
                del self.process_logs[script_id]

            logger.info(f"Stopped script id {script_id}")

            # Clean up
            del self.active_processes[script_id]
            if script_id in self.script_statuses:
                self.script_statuses[script_id]["status"] = "stopped"
                self.script_statuses[script_id]["stopped_at"] = time.time()

            # Update expect_status to stopped in database
            try:
                config = self._get_config()
                update_script_expect_status(config, script_id, 'stopped')
            except Exception as e:
                logger.warning(f"Failed to update expect_status for script {script_id}: {str(e)}")

            return True

        except Exception as e:
            logger.error(f"Failed to stop script id {script_id}: {str(e)}", exc_info=True)
            return False

    def get_script_status(self, script_id: int) -> Dict[str, Any]:
        """Get the status of a script for a user"""
        if script_id not in self.script_statuses:
            return {"status": "not_found", "script_id": script_id}

        status = self.script_statuses[script_id].copy()

        # Check if the process is still running
        if script_id in self.active_processes:
            process = self.active_processes[script_id]

            if process.poll() is None:
                # Verify the process actually exists in the system
                try:
                    p = psutil.Process(process.pid)
                    if not p.is_running():
                        # Process doesn't exist anymore
                        status["status"] = "stopped"
                        self.script_statuses[script_id]["status"] = "stopped"  # Update the stored status
                        self.script_statuses[script_id]["stopped_at"] = time.time()  # Record stop time
                        logger.warning(f"Process {process.pid} for script {script_id} not running but was in active_processes")
                        # Clean up the reference
                        del self.active_processes[script_id]
                        return status
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    # Process not found or can't access it
                    logger.warning(f"Error accessing process {script_id}: {str(e)}")
                    status["status"] = "unknown"
                    self.script_statuses[script_id]["status"] = "unknown"  # Update the stored status
                    return status

                # Process exists, get resource usage
                status["cpu_percent"] = p.cpu_percent(interval=0.1)
                status["memory_percent"] = p.memory_percent()
                status["memory_mb"] = p.memory_info().rss / (1024 * 1024)
                status["running_time"] = time.time() - status.get("started_at", time.time())
                status["running_time_formatted"] = self._format_time_duration(status["running_time"])
            else:
                # Process has ended
                status["status"] = "stopped"
                self.script_statuses[script_id]["status"] = "stopped"  # Update the stored status
                self.script_statuses[script_id]["stopped_at"] = time.time()  # Record stop time
                status["exit_code"] = process.returncode
                self.script_statuses[script_id]["exit_code"] = process.returncode  # Store exit code

                if process.returncode != 0:
                    logger.warning(f"Script {script_id} exited with non-zero code: {process.returncode}")

                # Get output if available
                try:
                    stdout, stderr = process.communicate(timeout=0.1)
                    status["last_stdout"] = stdout[-1000:] if stdout else ""
                    status["last_stderr"] = stderr[-1000:] if stderr else ""
                except subprocess.TimeoutExpired:
                    logger.warning(f"Timeout getting output from script {script_id}")
                    pass

                # Clean up the dead process
                del self.active_processes[script_id]

                # Close log files if they exist
                if script_id in self.process_logs:
                    try:
                        logger.debug(f"Closing log files for script {script_id} that ended unexpectedly")
                        if 'stdout' in self.process_logs[script_id]:
                            self.process_logs[script_id]['stdout'].close()
                        if 'stderr' in self.process_logs[script_id]:
                            self.process_logs[script_id]['stderr'].close()

                        # Add a stop marker to the log files
                        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                        stdout_log = status.get("stdout_log")
                        stderr_log = status.get("stderr_log")

                        if stdout_log:
                            with open(stdout_log, 'a') as f:
                                f.write(f"\n\n--- Script {script_id} stopped unexpectedly at {timestamp} with exit code {process.returncode} ---\n\n")
                        if stderr_log:
                            with open(stderr_log, 'a') as f:
                                f.write(f"\n\n--- Script {script_id} stopped unexpectedly at {timestamp} with exit code {process.returncode} ---\n\n")
                    except Exception as e:
                        logger.error(f"Error closing log files for script {script_id}: {str(e)}")

                    # Remove log file handles
                    del self.process_logs[script_id]

        # If the process is not in active_processes but status is still "running",
        # double-check if the process is actually running by PID
        if script_id not in self.active_processes and status.get("status") == "running" and "pid" in status:
            try:
                # Try to find the process by PID
                pid = status["pid"]
                try:
                    p = psutil.Process(pid)
                    if not p.is_running():
                        # Process doesn't exist anymore
                        logger.warning(f"Process {pid} for script {script_id} not running but status was 'running'")
                        status["status"] = "stopped"
                        self.script_statuses[script_id]["status"] = "stopped"  # Update the stored status
                        self.script_statuses[script_id]["stopped_at"] = time.time()  # Record stop time
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    # Process not found or can't access it
                    logger.warning(f"Process {pid} for script {script_id} not found but status was 'running': {str(e)}")
                    status["status"] = "stopped"
                    self.script_statuses[script_id]["status"] = "stopped"  # Update the stored status
                    self.script_statuses[script_id]["stopped_at"] = time.time()  # Record stop time
            except Exception as e:
                logger.error(f"Error checking process status for script {script_id}: {str(e)}")
                # Don't change status if we can't determine

        # Add log file content if available
        try:
            if "stdout_log" in status and os.path.exists(status["stdout_log"]):
                with open(status["stdout_log"], 'r') as f:
                    # Get the last 50 lines of the log file
                    lines = f.readlines()
                    status["recent_stdout"] = "".join(lines[-50:]) if lines else ""

            if "stderr_log" in status and os.path.exists(status["stderr_log"]):
                with open(status["stderr_log"], 'r') as f:
                    # Get the last 50 lines of the log file
                    lines = f.readlines()
                    status["recent_stderr"] = "".join(lines[-50:]) if lines else ""
        except Exception as e:
            logger.error(f"Error reading log files for script {script_id}: {str(e)}")
            status["log_error"] = str(e)

        return status

    def _format_time_duration(self, seconds: float) -> str:
        """Format a time duration in seconds to a human-readable string"""
        minutes, seconds = divmod(int(seconds), 60)
        hours, minutes = divmod(minutes, 60)
        days, hours = divmod(hours, 24)

        if days > 0:
            return f"{days}d {hours}h {minutes}m {seconds}s"
        elif hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"

    def _auto_restart_expected_scripts(self):
        """Auto-restart scripts that were expected to be running before shutdown"""
        try:
            # Get configuration for API requests
            config = self._get_config()

            # Get list of scripts that should be running
            expected_script_ids = get_expect_running_script_ids(config)

            if not expected_script_ids:
                logger.info("No scripts expected to be running on startup")
                return

            logger.info(f"Found {len(expected_script_ids)} scripts expected to be running: {expected_script_ids}")

            # Start each expected script
            for script_id in expected_script_ids:
                try:
                    logger.info(f"Auto-restarting script {script_id}")
                    success = self.start_script(script_id)
                    if success:
                        logger.info(f"Successfully auto-restarted script {script_id}")
                    else:
                        logger.error(f"Failed to auto-restart script {script_id}")
                except Exception as e:
                    logger.error(f"Error auto-restarting script {script_id}: {str(e)}", exc_info=True)

        except Exception as e:
            logger.error(f"Error during auto-restart process: {str(e)}", exc_info=True)
