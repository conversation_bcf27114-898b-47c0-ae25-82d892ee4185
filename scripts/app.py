# app.py
from fastapi import FastAPI, HTTPException
import uvicorn
import psutil
import os
import json
import logging
from typing import Dict, Any, Optional
from script_process_manager import ScriptProcessManager
import sys

log_level_name = os.environ.get('LOG_LEVEL', 'DEBUG')
log_level = getattr(logging, log_level_name.upper(), logging.DEBUG)

# Configure logging
logging.basicConfig(
    level=log_level,  # Changed from INFO to DEBUG
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI()
manager = ScriptProcessManager()

# API endpoints
@app.post("/scripts/start")
async def start_script(script_id: int):
    try:
        # Start the script process
        logger.info(f"Received request to start script with ID: {script_id}")
        success = manager.start_script(script_id)
    except Exception as e:
        logger.error(f"Error starting script with ID {script_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

    if success:
        logger.info(f"Successfully started script with ID: {script_id}")
        return {"status": "success", "message": f"Script started for user {script_id}"}
    else:
        logger.error(f"Failed to start script with ID: {script_id}")
        raise HTTPException(status_code=500, detail="Failed to start script")


@app.post("/scripts/stop")
async def stop_script(script_id: int):
    try:
        success = manager.stop_script(script_id)
    except Exception as e:
        logger.error(f"Error stopping script with ID {script_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

    if success:
        logger.info(f"Successfully stopped script with ID: {script_id}")
        return {"status": "success", "message": f"Script stopped for script id {script_id}"}
    else:
        logger.warning(f"Script with ID {script_id} not found or already stopped")
        raise HTTPException(status_code=404, detail="Script not found or already stopped")

@app.get("/scripts/status")
async def script_status(script_id: int):
    try:
        status = manager.get_script_status(script_id)
        # logger.debug(f"Script status: {status}")
        return status

    except Exception as e:
        logger.error(f"Error getting status for script with ID {script_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system/status")
async def system_status():
    logger.debug("Checking system status")
    status = {
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "active_scripts": len(manager.active_processes),
        "system_load": os.getloadavg()[0]
    }
    logger.debug(f"System status: {status}")
    return status

@app.get("/scripts/logs")
async def get_script_logs(script_id: int, lines: int = 100):
    """Get the logs for a script"""
    try:
        logger.info(f"Getting logs for script {script_id}, last {lines} lines")

        # Check if the script exists
        if script_id not in manager.script_statuses:
            logger.warning(f"Script {script_id} not found")
            raise HTTPException(status_code=404, detail=f"Script {script_id} not found")

        status = manager.script_statuses[script_id]

        # Get log file paths
        stdout_log = status.get("stdout_log")
        stderr_log = status.get("stderr_log")

        logs = {
            "script_id": script_id,
            "status": status.get("status", "unknown"),
            "stdout": "",
            "stderr": ""
        }

        # Read stdout log
        if stdout_log and os.path.exists(stdout_log):
            try:
                with open(stdout_log, 'r') as f:
                    all_lines = f.readlines()
                    logs["stdout"] = "".join(all_lines[-lines:]) if all_lines else ""
            except Exception as e:
                logger.error(f"Error reading stdout log for script {script_id}: {str(e)}")
                logs["stdout_error"] = str(e)

        # Read stderr log
        if stderr_log and os.path.exists(stderr_log):
            try:
                with open(stderr_log, 'r') as f:
                    all_lines = f.readlines()
                    logs["stderr"] = "".join(all_lines[-lines:]) if all_lines else ""
            except Exception as e:
                logger.error(f"Error reading stderr log for script {script_id}: {str(e)}")
                logs["stderr_error"] = str(e)

        return logs

    except Exception as e:
        logger.error(f"Error getting logs for script {script_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    # Log with both root logger and our module logger to ensure visibility
    logging.info("Starting FastAPI server on 0.0.0.0:8000")
    logger.info("Starting FastAPI server on 0.0.0.0:8000")

    # Configure Uvicorn with explicit log level
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="debug",  # Set Uvicorn log level to debug
        access_log=True     # Enable access logging
    )
