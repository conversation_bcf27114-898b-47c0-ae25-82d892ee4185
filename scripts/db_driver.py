import psycopg2
from psycopg2 import pool
import pandas as pd
import time, datetime, pytz, json
import logging
import sys
import os

log_level_name = os.environ.get('LOG_LEVEL', 'DEBUG')
log_level = getattr(logging, log_level_name.upper(), logging.DEBUG)

# Configure logging to debug level
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

class BeforeAfterMeta(type):
    def __new__(cls, clsname, bases, clsdict):
        for name, value in clsdict.items():
            if callable(value) and name not in ['__init__', '__new__', '__str__', 'get_connection', 'release_connection']:  # Exclude special methods
                clsdict[name] = cls.wrap_method(value)
        return super().__new__(cls, clsname, bases, clsdict)

    @staticmethod
    def wrap_method(method):
        def wrapper(self, *args, **kwargs):
            # Action before method call
            self.conn = self.get_connection()
            self.cur = self.conn.cursor()
            # Call the original method
            result = method(self, *args, **kwargs)
            logging.debug(f"DB query: {method.__name__}")
            # Action after method call
            self.release_connection(self.conn)
            return result
        return wrapper

class ConnectionPool(metaclass=BeforeAfterMeta):

    ORDER_STATUS = {
        'open': 0,
        'cancelled': 1,
        'waiting': 2,
        'executed': 3,
        'closed': 4
    }

    def __init__(self, connection_string):
        self.connection_string = connection_string
        min_conn = 1  # Minimum number of connections
        max_conn = 10  # Maximum number of connections
        self.connection_pool = pool.SimpleConnectionPool(minconn=min_conn, maxconn=max_conn, dsn=connection_string)

    # Function to get a connection from the pool
    def get_connection(self):
        try:
            conn = self.connection_pool.getconn()
        except:
            # Try to init again
            logging.debug("Connection DB lost. Wait 2s and try to reconnect to db")
            time.sleep(2)
            self.__init__(self.connection_string)
            conn = self.connection_pool.getconn()
        return conn

    def check_health(self):
        try:
            sql_query = """
                SELECT 1
            """
            self.cur.execute(sql_query)
            return True
        except Exception as e:
            logging.debug(f"Query Error: {e}")
            return e

    # Function to release a connection back to the pool
    def release_connection(self, conn):
        self.connection_pool.putconn(conn)

    def update_status_host_deal(self, host_deal_order_id, accountable_id, accountable_type, status):
        sql_query = """
            UPDATE deals SET status = %s WHERE order_id = %s AND accountable_id = %s AND accountable_type = %s;
        """
        values = (status, host_deal_order_id, accountable_id, accountable_type)
        # logging.debug(cur.mogrify(sql_query, values))
        self.cur.execute(sql_query, values)
        self.conn.commit()
        logging.debug('Update status host deal successfully')

    def update_status_slave_order(self, id, status):
        sql_query = """
            UPDATE slave_deals SET status = %s WHERE id = %s;
        """
        values = (status, id)
        self.cur.execute(sql_query, values)
        self.conn.commit()
        logging.debug('Update status slave deal successfully')

    def get_slave_deal_by_host_position_id(self, host_position_id, host_accountable_id, host_accountable_type, slave_accountable_id, slave_accountable_type):

        sql_query = """
            SELECT * FROM slave_deals
            WHERE host_deal_id = (SELECT id FROM deals WHERE position_id = %s AND accountable_id = %s AND accountable_type = %s)
            AND status != 1
            AND accountable_id = %s
            AND accountable_type = %s;
        """
        # Status in open, waiting
        values = (host_position_id, host_accountable_id, host_accountable_type, slave_accountable_id, slave_accountable_type)
        # logging.debug(cur.mogrify(sql_query, values))
        self.cur.execute(sql_query, values)
        return pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])

    def find_slave_deal(self, order_id, accountable_id, accountable_type):
        sql_query = """
            SELECT * from slave_deals
            WHERE order_id = %s AND accountable_id = %s AND accountable_type = %s
            LIMIT 1;
        """
        values = (order_id, accountable_id, accountable_type)
        self.cur.execute(sql_query, values)
        return pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])

    def get_host_deal_by_order_id(self, order_id, accountable_id, accountable_type):

        sql_query = """
            SELECT * FROM deals WHERE order_id = %s AND accountable_id = %s AND accountable_type = %s;
        """
        values = (order_id, accountable_id, accountable_type)
        # logging.debug(cur.mogrify(sql_query, values))
        self.cur.execute(sql_query, values)
        return pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])

    def get_host_deal_by_position(self, position_id, accountable_id, accountable_type='CtraderAccount'):

        sql_query = """
            SELECT * FROM deals WHERE position_id = %s AND accountable_id = %s AND accountable_type = %s;
        """
        values = (position_id, accountable_id, accountable_type)

        # logging.debug(cur.mogrify(sql_query, values))
        self.cur.execute(sql_query, values)
        return pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])


    def insert_slave_deals(self, order):
        sql_query = """
            INSERT INTO slave_deals (order_id, accountable_type, position_id, accountable_id, host_deal_id, status, pending_changes, script_id, created_at, updated_at, sl, tp)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), %s, %s) RETURNING *;
        """
        values = (order['order_id'], order['accountable_type'], order.get('position_id'), order['accountable_id'], order['host_deal_id'], order['status'], order.get('pending_changes'), order['script_id'], order.get('sl', 0.0), order.get('tp', 0.0))

        self.cur.execute(sql_query, values)
        df = pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])
        self.conn.commit()
        logging.debug(f"Data slave deal inserted successfully! order_id: {str(df.order_id[0])}")
        return df

    def update_sl_tp_slave_deal(self, id, sl, tp):
        sql_query = """
            UPDATE slave_deals SET sl = %s, tp = %s WHERE id = %s;
        """
        values = (sl, tp, int(id))
        self.cur.execute(sql_query, values)
        self.conn.commit()
        logging.debug(f"Update sl tp slave deal successfully with id {id}")

    def update_order_id_slave_deal(self, id, order_id):
        sql_query = """
            UPDATE slave_deals SET order_id = %s, position_id = %s WHERE id = %s;
        """
        values = (order_id, order_id, int(id))
        self.cur.execute(sql_query, values)
        self.conn.commit()
        logging.debug(f"Update order id slave deal successfully with id {id}")

    def apply_pending_changes_slave_deal(self, id, pending_changes):

        sql_query = """
            UPDATE slave_deals SET status = %s, pending_changes = %s WHERE id = %s;
        """
        values = (self.ORDER_STATUS['waiting'], pending_changes, int(id))
        self.cur.execute(sql_query, values)
        self.conn.commit()
        logging.debug('Update pending changes for slave deal in db')


    def insert_host_deal(self, order):

        sql_query = """
            INSERT INTO deals (order_id, position_id, accountable_type, accountable_id, status, trade_side, script_id, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW()) RETURNING *;
        """
        values = (order['order_id'], order['position_id'], order['accountable_type'], order['accountable_id'], order['status'], order['trade_side'], order['script_id'])

        self.cur.execute(sql_query, values)
        df = pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])
        self.conn.commit()
        logging.debug(f"Host deal inserted successfully! id: {df.id[0]}")
        return df




    def get_slave_accounts(self, script_id, ota='mt5'):
        sql_query = """
                SELECT "mt5_accounts".* FROM "mt5_accounts" INNER JOIN "accounts" ON "mt5_accounts"."id" = "accounts"."accountable_id" WHERE "accounts"."accountable_type" = 'Mt5Account' AND "accounts"."id" IN (SELECT "account_id" FROM "accounts_scripts" WHERE "script_id" = %s);
            """
        values = (int(script_id),)
        self.cur.execute(sql_query, values)
        df = pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])
        return df

    def find_slave_account(self, id):
        sql_query = """
                SELECT * FROM mt5_accounts WHERE id = %s
                LIMIT 1;
            """
        values = (int(id),)
        self.cur.execute(sql_query, values)
        df = pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])
        return df

    def delete_closed_host_deals(self):
        sql_query = """
                DELETE FROM deals
                WHERE status in (1, 4)
                RETURNING *;
            """
        self.cur.execute(sql_query)
        df = pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])
        self.conn.commit()
        return df

    def load_config(self, script_id):
        """
        Load configuration from the scripts table for a specific script.
        Returns the config as a Python dictionary or None if not found.
        """
        sql_query = """
            SELECT config FROM scripts WHERE id = %s LIMIT 1;
        """
        values = (int(script_id),)
        self.cur.execute(sql_query, values)
        result = self.cur.fetchone()

        if result and result[0]:
            try:
                return json.loads(result[0])
            except json.JSONDecodeError as e:
                logging.debug(f"Error parsing config JSON: {e}")
                return None
        return None

    def save_config(self, script_id, config_data):
        """
        Save configuration to the scripts table for a specific script.
        config_data should be a Python dictionary that will be converted to JSON.
        """
        config_json = json.dumps(config_data)
        sql_query = """
            UPDATE scripts SET config = %s WHERE id = %s RETURNING id;
        """
        values = (config_json, str(script_id))
        self.cur.execute(sql_query, values)
        result = self.cur.fetchone()
        self.conn.commit()

        if result:
            logging.debug(f"Config saved successfully for script ID: {result[0]}")
            return True
        else:
            logging.debug(f"Failed to save config for script ID: {script_id}")
            return False

    def get_ctrader_account_info(self, account_id):
        """
        Get CTrader account information by joining accounts and ctrader_accounts tables.

        Args:
            account_id: ID from the accounts table

        Returns:
            DataFrame containing the joined account information
        """
        sql_query = """
            SELECT a.id, a.name, c.account_id, c.access_token,
                c.refresh_token, c.expires_in, c.host_type, c.created_at, c.updated_at
            FROM accounts a
            JOIN ctrader_accounts c ON a.accountable_id = c.id
            WHERE a.id = %s AND a.accountable_type = 'CtraderAccount'
            LIMIT 1;
        """
        values = (str(account_id),)
        self.cur.execute(sql_query, values)
        df = pd.DataFrame(self.cur.fetchall(), columns=[desc[0] for desc in self.cur.description])
        return df
